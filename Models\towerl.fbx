; FBX 7.3.0 project file
; Copyright (C) 1997-2010 Autodesk Inc. and/or its licensors.
; All rights reserved.
; ----------------------------------------------------

FBXHeaderExtension:  {
	FBXHeaderVersion: 1003
	FBXVersion: 7300
	CreationTimeStamp:  {
		Version: 1000
		Year: 2025
		Month: 2
		Day: 20
		Hour: 16
		Minute: 41
		Second: 7
		Millisecond: 368
	}
	Creator: "FBX Unity Export version 1.1.1 (Originally created for the Unity Asset, Building Crafter)"
	SceneInfo: "SceneInfo::GlobalInfo", "UserData" {
		Type: "UserData"
		Version: 100
		MetaData:  {
			Version: 100
			Title: ""
			Subject: ""
			Author: ""
			Keywords: ""
			Revision: ""
			Comment: ""
		}
		Properties70:  {
			P: "DocumentUrl", "KString", "Url", "", "C:/Unity/BLAME/BLAME/AssetsAssets/_Game/Models/towerl.fbx.fbx"
			P: "SrcDocumentUrl", "KString", "Url", "", "C:/Unity/BLAME/BLAME/AssetsAssets/_Game/Models/towerl.fbx.fbx"
			P: "Original", "Compound", "", ""
			P: "Original|ApplicationVendor", "KString", "", "", ""
			P: "Original|ApplicationName", "KString", "", "", ""
			P: "Original|ApplicationVersion", "KString", "", "", ""
			P: "Original|DateTime_GMT", "DateTime", "", "", ""
			P: "Original|FileName", "KString", "", "", ""
			P: "LastSaved", "Compound", "", ""
			P: "LastSaved|ApplicationVendor", "KString", "", "", ""
			P: "LastSaved|ApplicationName", "KString", "", "", ""
			P: "LastSaved|ApplicationVersion", "KString", "", "", ""
			P: "LastSaved|DateTime_GMT", "DateTime", "", "", ""
		}
	}
}
GlobalSettings:  {
	Version: 1000
	Properties70:  {
		P: "UpAxis", "int", "Integer", "",1
		P: "UpAxisSign", "int", "Integer", "",1
		P: "FrontAxis", "int", "Integer", "",2
		P: "FrontAxisSign", "int", "Integer", "",1
		P: "CoordAxis", "int", "Integer", "",0
		P: "CoordAxisSign", "int", "Integer", "",1
		P: "OriginalUpAxis", "int", "Integer", "",-1
		P: "OriginalUpAxisSign", "int", "Integer", "",1
		P: "UnitScaleFactor", "double", "Number", "",100
		P: "OriginalUnitScaleFactor", "double", "Number", "",100
		P: "AmbientColor", "ColorRGB", "Color", "",0,0,0
		P: "DefaultCamera", "KString", "", "", "Producer Perspective"
		P: "TimeMode", "enum", "", "",11
		P: "TimeSpanStart", "KTime", "Time", "",0
		P: "TimeSpanStop", "KTime", "Time", "",479181389250
		P: "CustomFrameRate", "double", "Number", "",-1
	}
}
; Object definitions
;------------------------------------------------------------------

Definitions:  {
	Version: 100
	Count: 4
	ObjectType: "GlobalSettings" {
		Count: 1
	}
	ObjectType: "Model" {
		Count: 1
		PropertyTemplate: "FbxNode" {
			Properties70:  {
				P: "QuaternionInterpolate", "enum", "", "",0
				P: "RotationOffset", "Vector3D", "Vector", "",0,0,0
				P: "RotationPivot", "Vector3D", "Vector", "",0,0,0
				P: "ScalingOffset", "Vector3D", "Vector", "",0,0,0
				P: "ScalingPivot", "Vector3D", "Vector", "",0,0,0
				P: "TranslationActive", "bool", "", "",0
				P: "TranslationMin", "Vector3D", "Vector", "",0,0,0
				P: "TranslationMax", "Vector3D", "Vector", "",0,0,0
				P: "TranslationMinX", "bool", "", "",0
				P: "TranslationMinY", "bool", "", "",0
				P: "TranslationMinZ", "bool", "", "",0
				P: "TranslationMaxX", "bool", "", "",0
				P: "TranslationMaxY", "bool", "", "",0
				P: "TranslationMaxZ", "bool", "", "",0
				P: "RotationOrder", "enum", "", "",0
				P: "RotationSpaceForLimitOnly", "bool", "", "",0
				P: "RotationStiffnessX", "double", "Number", "",0
				P: "RotationStiffnessY", "double", "Number", "",0
				P: "RotationStiffnessZ", "double", "Number", "",0
				P: "AxisLen", "double", "Number", "",10
				P: "PreRotation", "Vector3D", "Vector", "",0,0,0
				P: "PostRotation", "Vector3D", "Vector", "",0,0,0
				P: "RotationActive", "bool", "", "",0
				P: "RotationMin", "Vector3D", "Vector", "",0,0,0
				P: "RotationMax", "Vector3D", "Vector", "",0,0,0
				P: "RotationMinX", "bool", "", "",0
				P: "RotationMinY", "bool", "", "",0
				P: "RotationMinZ", "bool", "", "",0
				P: "RotationMaxX", "bool", "", "",0
				P: "RotationMaxY", "bool", "", "",0
				P: "RotationMaxZ", "bool", "", "",0
				P: "InheritType", "enum", "", "",0
				P: "ScalingActive", "bool", "", "",0
				P: "ScalingMin", "Vector3D", "Vector", "",0,0,0
				P: "ScalingMax", "Vector3D", "Vector", "",1,1,1
				P: "ScalingMinX", "bool", "", "",0
				P: "ScalingMinY", "bool", "", "",0
				P: "ScalingMinZ", "bool", "", "",0
				P: "ScalingMaxX", "bool", "", "",0
				P: "ScalingMaxY", "bool", "", "",0
				P: "ScalingMaxZ", "bool", "", "",0
				P: "GeometricTranslation", "Vector3D", "Vector", "",0,0,0
				P: "GeometricRotation", "Vector3D", "Vector", "",0,0,0
				P: "GeometricScaling", "Vector3D", "Vector", "",1,1,1
				P: "MinDampRangeX", "double", "Number", "",0
				P: "MinDampRangeY", "double", "Number", "",0
				P: "MinDampRangeZ", "double", "Number", "",0
				P: "MaxDampRangeX", "double", "Number", "",0
				P: "MaxDampRangeY", "double", "Number", "",0
				P: "MaxDampRangeZ", "double", "Number", "",0
				P: "MinDampStrengthX", "double", "Number", "",0
				P: "MinDampStrengthY", "double", "Number", "",0
				P: "MinDampStrengthZ", "double", "Number", "",0
				P: "MaxDampStrengthX", "double", "Number", "",0
				P: "MaxDampStrengthY", "double", "Number", "",0
				P: "MaxDampStrengthZ", "double", "Number", "",0
				P: "PreferedAngleX", "double", "Number", "",0
				P: "PreferedAngleY", "double", "Number", "",0
				P: "PreferedAngleZ", "double", "Number", "",0
				P: "LookAtProperty", "object", "", ""
				P: "UpVectorProperty", "object", "", ""
				P: "Show", "bool", "", "",1
				P: "NegativePercentShapeSupport", "bool", "", "",1
				P: "DefaultAttributeIndex", "int", "Integer", "",-1
				P: "Freeze", "bool", "", "",0
				P: "LODBox", "bool", "", "",0
				P: "Lcl Translation", "Lcl Translation", "", "A",0,0,0
				P: "Lcl Rotation", "Lcl Rotation", "", "A",0,0,0
				P: "Lcl Scaling", "Lcl Scaling", "", "A",1,1,1
				P: "Visibility", "Visibility", "", "A",1
				P: "Visibility Inheritance", "Visibility Inheritance", "", "",1
			}
		}
	}
	ObjectType: "Geometry" {
		Count: 1
		PropertyTemplate: "FbxMesh" {
			Properties70:  {
				P: "Color", "ColorRGB", "Color", "",0.8,0.8,0.8
				P: "BBoxMin", "Vector3D", "Vector", "",0,0,0
				P: "BBoxMax", "Vector3D", "Vector", "",0,0,0
				P: "Primary Visibility", "bool", "", "",1
				P: "Casts Shadows", "bool", "", "",1
				P: "Receive Shadows", "bool", "", "",1
			}
		}
	}
	ObjectType: "Material" {
		Count: 1
		PropertyTemplate: "FbxSurfacePhong" {
			Properties70:  {
				P: "ShadingModel", "KString", "", "", "Phong"
				P: "MultiLayer", "bool", "", "",0
				P: "EmissiveColor", "Color", "", "A",0,0,0
				P: "EmissiveFactor", "Number", "", "A",1
				P: "AmbientColor", "Color", "", "A",0.2,0.2,0.2
				P: "AmbientFactor", "Number", "", "A",1
				P: "DiffuseColor", "Color", "", "A",0.8,0.8,0.8
				P: "DiffuseFactor", "Number", "", "A",1
				P: "Bump", "Vector3D", "Vector", "",0,0,0
				P: "NormalMap", "Vector3D", "Vector", "",0,0,0
				P: "BumpFactor", "double", "Number", "",1
				P: "TransparentColor", "Color", "", "A",0,0,0
				P: "TransparencyFactor", "Number", "", "A",0
				P: "DisplacementColor", "ColorRGB", "Color", "",0,0,0
				P: "DisplacementFactor", "double", "Number", "",1
				P: "VectorDisplacementColor", "ColorRGB", "Color", "",0,0,0
				P: "VectorDisplacementFactor", "double", "Number", "",1
				P: "SpecularColor", "Color", "", "A",0.2,0.2,0.2
				P: "SpecularFactor", "Number", "", "A",1
				P: "ShininessExponent", "Number", "", "A",20
				P: "ReflectionColor", "Color", "", "A",0,0,0
				P: "ReflectionFactor", "Number", "", "A",1
			}
		}
	}
	ObjectType: "Texture" {
		Count: 2
		PropertyTemplate: "FbxFileTexture" {
			Properties70:  {
				P: "TextureTypeUse", "enum", "", "",0
				P: "Texture alpha", "Number", "", "A",1
				P: "CurrentMappingType", "enum", "", "",0
				P: "WrapModeU", "enum", "", "",0
				P: "WrapModeV", "enum", "", "",0
				P: "UVSwap", "bool", "", "",0
				P: "PremultiplyAlpha", "bool", "", "",1
				P: "Translation", "Vector", "", "A",0,0,0
				P: "Rotation", "Vector", "", "A",0,0,0
				P: "Scaling", "Vector", "", "A",1,1,1
				P: "TextureRotationPivot", "Vector3D", "Vector", "",0,0,0
				P: "TextureScalingPivot", "Vector3D", "Vector", "",0,0,0
				P: "CurrentTextureBlendMode", "enum", "", "",1
				P: "UVSet", "KString", "", "", "default"
				P: "UseMaterial", "bool", "", "",0
				P: "UseMipMap", "bool", "", "",0
			}
		}
	}
}

; Object properties
;------------------------------------------------------------------

Objects:  {
	Model: 4685177763622214916, "Model::Model", "Null" {
		Version: 232
		Properties70:  {
			P: "RotationOrder", "enum", "", "",4
			P: "RotationActive", "bool", "", "",1
			P: "InheritType", "enum", "", "",1
			P: "ScalingMax", "Vector3D", "Vector", "",0,0,0
			P: "DefaultAttributeIndex", "int", "Integer", "",0
			P: "Lcl Translation", "Lcl Translation", "", "A+",0,0,0
			P: "Lcl Rotation", "Lcl Rotation", "", "A+",0,0,0
			P: "Lcl Scaling", "Lcl Scaling", "", "A",1,1,1
			P: "currentUVSet", "KString", "", "U", "map1"
		}
		Shading: T
		Culling: "CullingOff"
	}
	Model: 5227854380045796936, "Model::Prototype_512x512_Orange Mesh (1)", "Mesh" {
		Version: 232
		Properties70:  {
			P: "RotationOrder", "enum", "", "",4
			P: "RotationActive", "bool", "", "",1
			P: "InheritType", "enum", "", "",1
			P: "ScalingMax", "Vector3D", "Vector", "",0,0,0
			P: "DefaultAttributeIndex", "int", "Integer", "",0
			P: "Lcl Translation", "Lcl Translation", "", "A+",0,0,0
			P: "Lcl Rotation", "Lcl Rotation", "", "A+",0,0,0
			P: "Lcl Scaling", "Lcl Scaling", "", "A",1,1,1
			P: "currentUVSet", "KString", "", "U", "map1"
		}
		Shading: T
		Culling: "CullingOff"
	}
	Geometry: 4871820714250921269, "Geometry::", "Mesh" {
		Vertices: *324 {
			a: -25.64081,586.6196,121.6148,20.23615,586.6196,89.14456,77.34387,586.6196,48.72546,71.65515,586.6196,-113.7409,123.2209,586.6196,16.2551,16.25507,586.6196,-123.2209,-67.8638,586.6196,-104.1255,-77.34387,586.6196,-48.72542,113.7409,586.6196,71.65521,29.75906,586.6196,131.0948,-104.1255,586.6196,67.8638,-48.72546,586.6196,77.34387,-71.65518,586.6196,113.7409,-121.6148,586.6196,-25.64075,-89.14459,586.6196,20.23611,-131.0948,586.6196,29.75911,-113.7408,586.6196,-71.6552,-20.23611,586.6196,-89.14458,48.72546,586.6196,-77.34389,89.14459,586.6196,-20.23611,-29.75909,586.6196,-131.0948,131.0948,586.6196,-29.75909,113.7409,586.6196,71.65521,113.7409,-1689.88,71.65521,29.75906,-1689.88,131.0948,29.75906,586.6196,131.0948,29.75906,586.6196,131.0948,29.75906,-1689.88,131.0948,-71.65518,-1689.88,113.7409,-71.65518,586.6196,113.7409,-25.64081,586.6196,121.6148,-71.65518,586.6196,113.7409,-71.65518,-1689.88,113.7409,-131.0948,-1689.88,29.75911,-131.0948,586.6196,29.75911,-104.1255,586.6196,67.8638,-131.0948,586.6196,29.75911,-131.0948,-1689.88,29.75911,-113.7408,-1689.88,-71.6552,-113.7408,586.6196,-71.6552,-121.6148,586.6196,-25.64075,71.65515,586.6196,-113.7409,71.65515,-1689.88,-113.7409,131.0948,-1689.88,-29.75909,131.0948,586.6196,-29.75909,-113.7408,586.6196,-71.6552,-113.7408,-1689.88,-71.6552,-29.75909,-1689.88,-131.0948,-29.75909,586.6196,-131.0948,-67.8638,586.6196,-104.1255,-131.0948,-1689.88,29.75911,-71.65518,-1689.88,113.7409,29.75906,-1689.88,131.0948,113.7409,-1689.88,71.65521,131.0948,-1689.88,-29.75909,71.65515,-1689.88,-113.7409,-29.75909,-1689.88,-131.0948,-113.7408,-1689.88,-71.6552,131.0948,586.6196,-29.75909,131.0948,-1689.88,-29.75909,113.7409,-1689.88,71.65521,113.7409,586.6196,71.65521,123.2209,586.6196,16.2551,-29.75909,586.6196,-131.0948,-29.75909,-1689.88,-131.0948,71.65515,-1689.88,-113.7409,71.65515,586.6196,-113.7409,16.25507,586.6196,-123.2209,-20.23621,1689.88,-89.14451,48.72552,1689.88,-77.34379,89.14453,1689.88,-20.23621,77.34381,1689.88,48.72549,20.23618,1689.88,89.1445,-48.72552,1689.88,77.34378,-89.1445,1689.88,20.23624,-77.34375,1689.88,-48.72549,77.34387,586.6196,48.72543,20.23611,586.6196,89.14456,20.23618,1689.88,89.1445,77.34381,1689.88,48.72549,20.23611,586.6196,89.14456,-48.72543,586.6196,77.34387,-48.72552,1689.88,77.34378,20.23618,1689.88,89.1445,-48.72543,586.6196,77.34387,-89.14456,586.6196,20.23611,-89.1445,1689.88,20.23624,-48.72552,1689.88,77.34378,-89.14456,586.6196,20.23611,-77.34387,586.6196,-48.72543,-77.34375,1689.88,-48.72549,-89.1445,1689.88,20.23624,48.7254,586.6196,-77.34389,89.14459,586.6196,-20.23611,89.14453,1689.88,-20.23621,48.72552,1689.88,-77.34379,89.14459,586.6196,-20.23611,77.34387,586.6196,48.72543,77.34381,1689.88,48.72549,89.14453,1689.88,-20.23621,-77.34387,586.6196,-48.72543,-20.23611,586.6196,-89.14457,-20.23621,1689.88,-89.14451,-77.34375,1689.88,-48.72549,-20.23611,586.6196,-89.14457,48.7254,586.6196,-77.34389,48.72552,1689.88,-77.34379,-20.23621,1689.88,-89.14451
		} 
		PolygonVertexIndex: *216 {
			a: 0,2,-2,3,5,-5,6,7,-6,0,8,-5,0,9,-9,10,1,-12,10,0,-2,10,12,-1,13,11,-15,13,10,-12,13,15,-11,6,14,-8,6,13,-15,6,16,-14,7,17,-6,17,18,-6,0,4,-3,19,2,-5,18,19,-5,20,6,-6,21,3,-5,5,18,-5,22,24,-24,22,25,-25,26,28,-28,26,29,-29,26,30,-30,31,33,-33,31,34,-34,31,35,-35,36,38,-38,36,39,-39,36,40,-40,41,43,-43,41,44,-44,45,47,-47,45,48,-48,45,49,-49,50,52,-52,50,53,-53,50,54,-54,50,55,-55,50,56,-56,50,57,-57,58,60,-60,58,61,-61,58,62,-62,63,65,-65,63,66,-66,63,67,-67,68,70,-70,68,71,-71,68,72,-72,68,73,-73,68,74,-74,68,75,-75,76,78,-78,76,79,-79,80,82,-82,80,83,-83,84,86,-86,84,87,-87,88,90,-90,88,91,-91,92,94,-94,92,95,-95,96,98,-98,96,99,-99,100,102,-102,100,103,-103,104,106,-106,104,107,-107
		} 
		GeometryVersion: 124
		LayerElementNormal: 0 {
			Version: 101
			Name: ""
			MappingInformationType: "ByPolygonVertex"
			ReferenceInformationType: "Direct"
			Normals: *648 {
				a: 0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0.8460967,0,0.5330294,0.2213721,0,0.9751894,0.8460967,0,0.5330294,0.8460967,0,0.5330294,0.2213721,0,0.9751894,0.2213721,0,0.9751894,0.2213721,0,0.9751894,-0.5330292,0,0.8460968,0.2213721,0,0.9751894,0.2213721,0,0.9751894,-0.5330292,0,0.8460968,-0.5330292,0,0.8460968,0.2213721,0,0.9751894,-0.1686676,0,0.985673,-0.5330292,0,0.8460968,-0.5330292,0,0.8460969,-0.9751894,0,0.2213721,-0.5330292,0,0.8460968,-0.5330292,0,0.8460969,-0.9751894,0,0.2213721,-0.9751894,0,0.2213721,-0.5330292,0,0.8460969,-0.816242,0,0.5777102,-0.9751894,0,0.2213721,-0.9751894,0,0.2213721,-0.8460968,0,-0.5330293,-0.9751894,0,0.2213721,-0.9751894,0,0.2213721,-0.8460968,0,-0.5330294,-0.8460968,0,-0.5330293,-0.9751894,0,0.2213721,-0.985673,0,-0.1686679,-0.8460968,0,-0.5330294,0.5330292,0,-0.8460969,0.9751894,0,-0.2213723,0.5330291,0,-0.8460969,0.5330292,0,-0.8460969,0.9751995,0,-0.2213279,0.9751894,0,-0.2213723,-0.8460968,0,-0.5330294,-0.2213722,0,-0.9751894,-0.8460968,0,-0.5330293,-0.8460968,0,-0.5330294,-0.2213721,0,-0.9751894,-0.2213722,0,-0.9751894,-0.8460968,0,-0.5330294,-0.5777102,0,-0.816242,-0.2213721,0,-0.9751894,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0.9751995,0,-0.2213279,0.8460967,0,0.5330294,0.9751894,0,-0.2213723,0.9751995,0,-0.2213279,0.8460967,0,0.5330294,0.8460967,0,0.5330294,0.9751995,0,-0.2213279,0.985673,0,0.1686677,0.8460967,0,0.5330294,-0.2213721,0,-0.9751894,0.5330291,0,-0.8460969,-0.2213722,0,-0.9751894,-0.2213721,0,-0.9751894,0.5330292,0,-0.8460969,0.5330291,0,-0.8460969,-0.2213721,0,-0.9751894,0.1686678,0,-0.985673,0.5330292,0,-0.8460969,-2.331466E-15,0.68,3.776299E-15,-2.331466E-15,0.68,3.776299E-15,-2.331466E-15,0.68,3.776299E-15,-2.331466E-15,0.68,3.776299E-15,-2.331466E-15,0.68,3.776299E-15,-2.331466E-15,0.68,3.776299E-15,-2.331466E-15,0.68,3.776299E-15,-2.331466E-15,0.68,3.776299E-15,-2.331466E-15,0.68,3.776299E-15,-2.331466E-15,0.68,3.776299E-15,-2.331466E-15,0.68,3.776299E-15,-2.331466E-15,0.68,3.776299E-15,-2.331466E-15,0.68,3.776299E-15,-2.331466E-15,0.68,3.776299E-15,-2.331466E-15,0.68,3.776299E-15,-2.331466E-15,0.68,3.776299E-15,-2.331466E-15,0.68,3.776299E-15,-2.331466E-15,0.68,3.776299E-15,0.8460968,3.566662E-08,0.5330292,0.2213722,3.387226E-08,0.9751894,0.2213722,3.387226E-08,0.9751894,0.8460968,3.566662E-08,0.5330292,0.8460968,3.566662E-08,0.5330293,0.2213722,3.387226E-08,0.9751894,0.2213722,3.387226E-08,0.9751894,-0.5330291,3.04854E-08,0.8460969,-0.5330291,3.04854E-08,0.8460969,0.2213722,3.387226E-08,0.9751894,0.2213722,3.387226E-08,0.9751894,-0.5330291,3.04854E-08,0.8460969,-0.5330291,3.04854E-08,0.8460969,-0.9751894,4.229381E-08,0.2213723,-0.9751894,4.229382E-08,0.2213722,-0.5330291,3.04854E-08,0.8460969,-0.5330291,3.048539E-08,0.8460969,-0.9751894,4.229381E-08,0.2213723,-0.9751894,4.229382E-08,0.2213722,-0.8460969,4.590846E-08,-0.5330292,-0.8460969,4.590846E-08,-0.5330291,-0.9751894,4.229382E-08,0.2213722,-0.9751894,4.229382E-08,0.2213723,-0.8460969,4.590846E-08,-0.5330292,0.5330291,3.670276E-08,-0.8460969,0.9751893,3.263303E-08,-0.2213724,0.9751893,3.263303E-08,-0.2213724,0.5330291,3.670276E-08,-0.8460969,0.5330291,3.670275E-08,-0.8460969,0.9751893,3.263303E-08,-0.2213724,0.9751893,3.263303E-08,-0.2213724,0.8460968,3.566662E-08,0.5330292,0.8460968,3.566662E-08,0.5330292,0.9751893,3.263303E-08,-0.2213724,0.9751893,3.263303E-08,-0.2213724,0.8460968,3.566662E-08,0.5330292,-0.8460969,4.590846E-08,-0.5330291,-0.2213724,3.996423E-08,-0.9751893,-0.2213724,3.996423E-08,-0.9751893,-0.8460969,4.590846E-08,-0.5330291,-0.8460969,4.590846E-08,-0.5330292,-0.2213724,3.996423E-08,-0.9751893,-0.2213724,3.996423E-08,-0.9751893,0.533029,3.670276E-08,-0.8460969,0.5330291,3.670276E-08,-0.8460969,-0.2213724,3.996423E-08,-0.9751893,-0.2213724,3.996423E-08,-0.9751893,0.533029,3.670276E-08,-0.8460969
			}
		}

		LayerElementTangent: 0 {
			Version: 101
			Name: ""
			MappingInformationType: "ByPolygonVertex"
			ReferenceInformationType: "Direct"
			Tangents: *648 {
				a: 1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0.5330294,0,-0.8460967,0.9751894,0,-0.221372,0.5330294,0,-0.8460967,0.5330294,0,-0.8460967,0.9751894,0,-0.2213721,0.9751894,0,-0.221372,0.9751894,0,-0.2213721,0.8460969,0,0.5330291,0.9751894,0,-0.2213721,0.9751894,0,-0.2213721,0.8460969,0,0.5330291,0.8460969,0,0.5330291,0.9751894,0,-0.2213721,NaN,NaN,NaN,0.8460969,0,0.5330291,0.8460969,-9.96661E-17,0.5330291,0.2213722,-1.494991E-16,0.9751894,0.8460968,0,0.5330292,0.8460969,-9.96661E-17,0.5330291,0.221372,-1.494992E-16,0.9751894,0.2213722,-1.494991E-16,0.9751894,0.8460969,-9.96661E-17,0.5330291,0.5777102,0,0.816242,0.221372,-1.494992E-16,0.9751894,0.2213721,0,0.9751894,-0.5330293,0,0.8460968,0.221372,0,0.9751894,0.2213721,0,0.9751894,-0.5330294,0,0.8460968,-0.5330293,0,0.8460968,0.2213721,0,0.9751894,NaN,NaN,NaN,-0.5330294,0,0.8460968,-0.8460969,5.979963E-16,-0.5330292,-0.2213723,5.979965E-16,-0.9751894,-0.8460969,0,-0.5330291,-0.8460969,5.979963E-16,-0.5330292,-0.2213279,1.196016E-15,-0.9751995,-0.2213723,5.979965E-16,-0.9751894,-0.5330293,0,0.8460968,-0.9751894,0,0.2213722,-0.5330293,0,0.8460968,-0.5330293,0,0.8460968,-0.9751894,0,0.2213722,-0.9751894,0,0.2213722,-0.5330293,0,0.8460968,NaN,NaN,NaN,-0.9751894,0,0.2213722,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-0.2213279,-3.986567E-16,-0.9751995,0.5330294,-5.979963E-16,-0.8460967,-0.2213723,0,-0.9751894,-0.2213279,-3.986567E-16,-0.9751995,0.5330294,-5.979964E-16,-0.8460967,0.5330294,-5.979963E-16,-0.8460967,-0.2213279,-3.986567E-16,-0.9751995,0.1686677,0,-0.985673,0.5330294,-5.979964E-16,-0.8460967,-0.9751894,0,0.2213721,-0.8460969,0,-0.5330291,-0.9751894,0,0.2213722,-0.9751894,0,0.2213721,-0.8460968,0,-0.5330292,-0.8460969,0,-0.5330291,-0.9751894,0,0.2213721,NaN,NaN,NaN,-0.8460968,0,-0.5330292,1,1.585397E-15,1.575257E-08,1,1.585397E-15,1.575255E-08,1,1.585397E-15,-4.796387E-08,1,1.585397E-15,1.575257E-08,1,1.585397E-15,-7.265165E-08,1,1.585397E-15,1.575255E-08,1,1.585397E-15,1.575257E-08,1,1.585397E-15,7.31441E-14,1,1.585397E-15,-7.265165E-08,1,1.585397E-15,1.575257E-08,1,1.585396E-15,1.918552E-07,1,1.585397E-15,7.31441E-14,1,1.585397E-15,1.575257E-08,1,1.585397E-15,3.150511E-08,1,1.585396E-15,1.918552E-07,1,1.585397E-15,1.575257E-08,1,1.585397E-15,-9.592785E-08,1,1.585397E-15,3.150511E-08,0.5330293,-1.477357E-08,-0.8460968,0.9751894,1.403038E-08,-0.2213722,0.9751894,1.403039E-08,-0.2213722,0.5330293,-1.477357E-08,-0.8460968,0.5330293,-1.477357E-08,-0.8460968,0.9751894,1.403038E-08,-0.2213722,0.9751894,-1.403036E-08,-0.2213722,0.8460969,1.262745E-08,0.5330292,0.8460969,1.262746E-08,0.5330291,0.9751894,-1.403036E-08,-0.2213722,0.9751894,-1.403038E-08,-0.2213722,0.8460969,1.262745E-08,0.5330292,0.8460969,-1.262744E-08,0.5330292,0.2213722,1.751871E-08,0.9751894,0.2213722,1.751872E-08,0.9751894,0.8460969,-1.262744E-08,0.5330292,0.8460969,-1.262743E-08,0.5330291,0.2213722,1.751871E-08,0.9751894,0.2213722,-1.751872E-08,0.9751894,-0.5330292,1.901586E-08,0.8460968,-0.5330291,1.901587E-08,0.8460969,0.2213722,-1.751872E-08,0.9751894,0.2213723,-1.751873E-08,0.9751894,-0.5330292,1.901586E-08,0.8460968,-0.8460969,-1.520273E-08,-0.5330291,-0.2213724,1.351708E-08,-0.9751893,-0.2213724,1.35171E-08,-0.9751893,-0.8460969,-1.520273E-08,-0.5330291,-0.8460969,-1.520274E-08,-0.5330291,-0.2213724,1.351708E-08,-0.9751893,-0.2213723,-1.351706E-08,-0.9751894,0.5330291,1.477358E-08,-0.8460969,0.5330292,1.477359E-08,-0.8460968,-0.2213723,-1.351706E-08,-0.9751894,-0.2213724,-1.351708E-08,-0.9751893,0.5330291,1.477358E-08,-0.8460969,-0.5330291,-1.901585E-08,0.8460969,-0.9751893,1.655377E-08,0.2213725,-0.9751893,1.655376E-08,0.2213725,-0.5330291,-1.901585E-08,0.8460969,-0.5330292,-1.901584E-08,0.8460969,-0.9751893,1.655377E-08,0.2213725,-0.9751893,-1.655376E-08,0.2213724,-0.846097,1.520274E-08,-0.533029,-0.8460969,1.520275E-08,-0.5330291,-0.9751893,-1.655376E-08,0.2213724,-0.9751893,-1.655377E-08,0.2213724,-0.846097,1.520274E-08,-0.533029
			}
		}

		LayerElementBinormal: 0 {
			Version: 101
			Name: ""
			MappingInformationType: "ByPolygonVertex"
			ReferenceInformationType: "Direct"
			Binormals: *648 {
				a: 0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0.9999999,0,0,1,0,0,1,0,0,0.9999999,0,0,0.9999999,0,0,1,0,NaN,NaN,NaN,0,0.9999999,0,8.432717E-17,1,5.312494E-17,3.309494E-17,0.9999999,1.4579E-16,0,0.9999999,0,8.432717E-17,1,5.312494E-17,3.309494E-17,1,1.4579E-16,3.309494E-17,0.9999999,1.4579E-16,8.432717E-17,1,5.312494E-17,0,1,0,3.309494E-17,1,1.4579E-16,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,NaN,NaN,NaN,0,1,0,5.059628E-16,1,3.187495E-16,1.323799E-16,1,5.831598E-16,0,1,0,5.059628E-16,1,3.187495E-16,2.647116E-16,1,1.166354E-15,1.323799E-16,1,5.831598E-16,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,NaN,NaN,NaN,0,1,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,-8.823385E-17,1,-3.887698E-16,3.187496E-16,1,-5.059627E-16,0,1,0,-8.823385E-17,1,-3.887698E-16,3.187497E-16,1,-5.059627E-16,3.187496E-16,1,-5.059627E-16,-8.823385E-17,1,-3.887698E-16,0,1,0,3.187497E-16,1,-5.059627E-16,0,0.9999999,0,0,1,0,0,1,0,0,0.9999999,0,0,1,0,0,1,0,0,0.9999999,0,NaN,NaN,NaN,0,1,0,1.071175E-08,3.776299E-15,-0.68,1.071173E-08,3.776299E-15,-0.68,-3.261543E-08,3.776299E-15,-0.68,1.071175E-08,3.776299E-15,-0.68,-4.940312E-08,3.776299E-15,-0.68,1.071173E-08,3.776299E-15,-0.68,1.071175E-08,3.776299E-15,-0.68,4.973799E-14,3.776299E-15,-0.68,-4.940312E-08,3.776299E-15,-0.68,1.071175E-08,3.776299E-15,-0.68,1.304616E-07,3.7763E-15,-0.68,4.973799E-14,3.776299E-15,-0.68,1.071175E-08,3.776299E-15,-0.68,2.142348E-08,3.776299E-15,-0.68,1.304616E-07,3.7763E-15,-0.68,1.071175E-08,3.776299E-15,-0.68,-6.523094E-08,3.776299E-15,-0.68,2.142348E-08,3.776299E-15,-0.68,-2.230267E-08,1,-3.151122E-08,-2.118065E-08,1,-2.992593E-08,-2.118066E-08,1,-2.992593E-08,-2.230267E-08,1,-3.151122E-08,-2.230266E-08,1,-3.151122E-08,-2.118065E-08,1,-2.992593E-08,6.183885E-09,1,-3.61378E-08,5.565562E-09,1,-3.252439E-08,5.565552E-09,1,-3.25244E-08,6.183885E-09,1,-3.61378E-08,6.183897E-09,1,-3.61378E-08,5.565562E-09,1,-3.252439E-08,2.693364E-08,1,-1.906281E-08,3.736632E-08,1,-2.644674E-08,3.736633E-08,1,-2.644675E-08,2.693364E-08,1,-1.906281E-08,2.693363E-08,1,-1.90628E-08,3.736632E-08,1,-2.644674E-08,4.512264E-08,1,7.721389E-09,4.897901E-08,1,8.381289E-09,4.897901E-08,1,8.381282E-09,4.512264E-08,1,7.721389E-09,4.512264E-08,1,7.721398E-09,4.897901E-08,1,8.381289E-09,-3.242662E-08,1,2.295059E-08,-2.883107E-08,1,2.040577E-08,-2.883107E-08,0.9999999,2.040578E-08,-3.242662E-08,1,2.295059E-08,-3.242663E-08,1,2.295058E-08,-2.883107E-08,1,2.040577E-08,-3.481569E-08,1,-5.957647E-09,-3.805216E-08,0.9999999,-6.511472E-09,-3.805216E-08,0.9999999,-6.511462E-09,-3.481569E-08,1,-5.957647E-09,-3.481569E-08,0.9999999,-5.957657E-09,-3.805216E-08,0.9999999,-6.511472E-09,2.8707E-08,1,4.055979E-08,2.499004E-08,1,3.530814E-08,2.499003E-08,1,3.530814E-08,2.8707E-08,1,4.055979E-08,2.8707E-08,1,4.055979E-08,2.499004E-08,1,3.530814E-08,-7.296077E-09,0.9999999,4.263724E-08,-6.700642E-09,1,3.915759E-08,-6.700641E-09,1,3.91576E-08,-7.296077E-09,0.9999999,4.263724E-08,-7.29608E-09,1,4.263724E-08,-6.700642E-09,1,3.915759E-08
			}
		}
		LayerElementUV: 0 {
			Version: 101
			Name: "map1"
			MappingInformationType: "ByPolygonVertex"
			ReferenceInformationType: "IndexToDirect"
			UV: *216 {
				a: 439.9842,-313.8648,485.8611,-281.3946,542.9689,-240.9755,537.2802,-78.50913,588.8459,-208.5051,481.8801,-69.02909,397.7612,-88.12448,388.2811,-143.5246,579.3659,-263.9052,495.3841,-323.3448,361.4995,-260.1138,416.8995,-269.5939,393.9698,-305.9909,344.0102,-166.6093,376.4804,-212.4861,334.5302,-222.0091,351.8842,-120.5948,445.3889,-103.1054,514.3505,-114.9061,554.7696,-172.0139,435.8659,-61.15518,596.7198,-162.4909,-656.8442,-7.656781E-08,-656.8442,-2276.499,-759.7192,-2276.499,-759.7192,-7.656781E-08,-668.6256,-6.551053E-08,-668.6256,-2276.499,-771.5005,-2276.499,-771.5005,-6.551053E-08,-724.8234,-6.551053E-08,-725.6205,-4.797063E-08,-725.6205,-2276.499,-828.4955,-2276.499,-828.4955,-4.797066E-08,-781.8184,-4.797067E-08,-854.7048,-2.19109E-07,-854.7048,-2276.499,-957.5799,-2276.499,-957.5799,-2.19109E-07,-910.9027,-2.19109E-07,-560.2493,-8.616297E-08,-560.2493,-2276.499,-663.1243,-2276.499,-663.1243,-8.616286E-08,-217.5256,-1.081905E-07,-217.5256,-2276.499,-320.4005,-2276.499,-320.4005,-1.081905E-07,-273.7235,-1.081905E-07,-334.5302,-222.0083,-393.9698,-305.9901,-495.3841,-323.344,-579.3659,-263.9044,-596.7198,-162.4901,-537.2802,-78.50831,-435.8659,-61.15436,-351.8842,-120.594,-636.9149,7.198491E-08,-636.9149,-2276.499,-739.7899,-2276.499,-739.7899,7.19848E-08,-683.5919,7.198491E-08,-411.4942,-6.516402E-08,-411.4942,-2276.499,-514.3692,-2276.499,-514.3692,-6.516402E-08,-458.1712,-6.516402E-08,654.9835,-151.6257,756.3978,-168.9797,815.8375,-252.9615,798.4835,-354.3757,714.5017,-413.8154,613.0875,-396.4614,553.6478,-312.4798,571.0018,-211.0654,-530.2739,-2.933182E-06,-633.1493,-2.933182E-06,-633.1492,1622.441,-530.274,1622.441,-437.4177,-2.170584E-05,-540.2925,-2.170584E-05,-540.2926,1622.441,-437.4176,1622.441,-525.2137,-6.075907E-07,-628.089,-6.075907E-07,-628.0889,1622.441,-525.2139,1622.441,-802.4957,5.359185E-05,-905.3705,5.359185E-05,-905.3706,1622.441,-802.4955,1622.441,-760.6559,4.111495E-07,-863.5313,4.111495E-07,-863.5311,1622.441,-760.6561,1622.441,-689.1241,-5.031138E-05,-791.9989,-5.031138E-05,-791.999,1622.441,-689.124,1622.441,-344.0958,1.948623E-06,-446.9711,1.948623E-06,-446.9709,1622.441,-344.0959,1622.441,-642.702,3.092E-06,-745.5768,3.092E-06,-745.577,1622.441,-642.7019,1622.441
				}
			UVIndex: *216 {
				a: 0,2,1,3,5,4,6,7,5,0,8,4,0,9,8,10,1,11,10,0,1,10,12,0,13,11,14,13,10,11,13,15,10,6,14,7,6,13,14,6,16,13,7,17,5,17,18,5,0,4,2,19,2,4,18,19,4,20,6,5,21,3,4,5,18,4,22,24,23,22,25,24,26,28,27,26,29,28,26,30,29,31,33,32,31,34,33,31,35,34,36,38,37,36,39,38,36,40,39,41,43,42,41,44,43,45,47,46,45,48,47,45,49,48,50,52,51,50,53,52,50,54,53,50,55,54,50,56,55,50,57,56,58,60,59,58,61,60,58,62,61,63,65,64,63,66,65,63,67,66,68,70,69,68,71,70,68,72,71,68,73,72,68,74,73,68,75,74,76,78,77,76,79,78,80,82,81,80,83,82,84,86,85,84,87,86,88,90,89,88,91,90,92,94,93,92,95,94,96,98,97,96,99,98,100,102,101,100,103,102,104,106,105,104,107,106
			}
		}
		LayerElementMaterial: 0 {
			Version: 101
			Name: ""
			MappingInformationType: "ByPolygon"
			ReferenceInformationType: "IndexToDirect"
			Materials: *72 {
				a: 0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
			} 
		}
		Layer: 0 {
			Version: 100
			LayerElement:  {
				Type: "LayerElementNormal"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementTangent"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementBinormal"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementMaterial"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementTexture"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementUV"
				TypedIndex: 0
			}
		}
	}

	Material: 48458, "Material::Prototype_512x512_Orange", "" {
		Version: 102
		ShadingModel: "phong"
		MultiLayer: 0
		Properties70:  {
			P: "Diffuse", "Vector3D", "Vector", "",1,1,1
			P: "DiffuseColor", "Color", "", "A",1,1,1
			P: "Emissive", "Vector3D", "Vector", "",1,1,1
			P: "EmissiveFactor", "Number", "", "A",1
		}
	}
	Texture: 4838849728112202344, "Texture::Prototype_512x512_Orange", "" {
		Type: "TextureVideoClip"
		Version: 202
		TextureName: "Texture::Prototype_512x512_Orange"
		Properties70:  {
			P: "CurrentTextureBlendMode", "enum", "", "",0
			P: "UVSet", "KString", "", "", "map1"
			P: "UseMaterial", "bool", "", "",1
		}
		Media: "Video::Prototype_512x512_Orange"
		FileName: "Assets/_Game/Materials/Gridbox Prototype Materials/Textures/prototype_512x512_orange.png"
		ModelUVTranslation: 0,0
		ModelUVScaling: 1,1
		Texture_Alpha_Source: "None"
		Cropping: 0,0,0,0
	}

}
; Object connections
;------------------------------------------------------------------

Connections:  {
	
	;Model::Model, Model::RootNode
	C: "OO",4685177763622214916,0

	;Model::<Renderable generated -80152>(Clone), Model::USING PARENT
	C: "OO",5227854380045796936,4685177763622214916

	;Geometry::, Model::<Renderable generated -80152>(Clone)
	C: "OO",4871820714250921269,5227854380045796936

	;Material::Prototype_512x512_Orange, Model::<Renderable generated -80152>(Clone)
	C: "OO",48458,5227854380045796936

	;Texture::prototype_512x512_orange, Material::Prototype_512x512_Orange"
	C: "OP",4838849728112202344,48458, "DiffuseColor"


}
