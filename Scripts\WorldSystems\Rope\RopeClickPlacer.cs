using UnityEngine;
using System.Collections.Generic;

public class RopeClickPlacer : MonoBehaviour
{
    [Header("Rope Settings")]
    [SerializeField] private LayerMask clickableLayers = -1;
    [SerializeField] private Material ropeMaterial;
    [SerializeField] private float defaultRopeLength = 5f;
    [SerializeField] private bool autoCalculateLength = true;
    [SerializeField] private float lengthMultiplier = 1.2f; // Extra slack
    
    [Header("Rope Physics Settings")]
    [SerializeField] private int ropeSegments = 24;
    [SerializeField] private float ropeRadius = 0.05f;
    [SerializeField] private float ropeMass = 1f;
    [SerializeField] private float ropeStiffness = 0.8f;
    [SerializeField] private bool enableCollisions = true;
    [SerializeField] private LayerMask collisionLayers = -1;
    
    [Header("Visual Feedback")]
    [SerializeField] private Material previewLineMaterial;
    [SerializeField] private float previewLineWidth = 0.1f;
    [SerializeField] private Color validPlacementColor = Color.green;
    [SerializeField] private Color invalidPlacementColor = Color.red;
    [SerializeField] private GameObject anchorPointPrefab; // Optional visual for anchor points
    
    [Header("Camera Settings")]
    [SerializeField] private Camera playerCamera;
    [SerializeField] private float maxPlacementDistance = 50f;
    
    [Header("Attachment Settings")]
    [SerializeField] private bool attachToRigidbodies = true;
    [SerializeField] private bool createAnchorsIfNoHit = false;
    
    // State
    private Vector3? firstClickPoint = null;
    private Transform firstAttachment = null;
    private GameObject firstAnchorVisual = null;
    private LineRenderer previewLine;
    private List<GameObject> createdRopes = new List<GameObject>();
    private bool isPlacementValid = false;
    
    void Start()
    {
        // Get camera if not assigned
        if (playerCamera == null)
            playerCamera = Camera.main;
        
        if (playerCamera == null)
            playerCamera = FindObjectOfType<Camera>();
        
        // Create preview line
        CreatePreviewLine();
        
        // Set default collision layers if not set
        if (collisionLayers == 0)
            collisionLayers = clickableLayers;
    }
    
    void Update()
    {
        HandleInput();
        UpdatePreviewLine();
    }
    
    private void HandleInput()
    {
        // Place rope with E key
        if (Input.GetKeyDown(KeyCode.E))
        {
            HandlePlacement();
        }
        
        // Cancel with Escape or right click
        if ((Input.GetKeyDown(KeyCode.Escape) || Input.GetMouseButtonDown(1)) && firstClickPoint != null)
        {
            CancelPlacement();
        }
        
        // Undo last rope with Z
        if (Input.GetKeyDown(KeyCode.Z) && Input.GetKey(KeyCode.LeftControl))
        {
            UndoLastRope();
        }
        
        // Clear all ropes with C
        if (Input.GetKeyDown(KeyCode.C) && Input.GetKey(KeyCode.LeftShift))
        {
            ClearAllRopes();
        }
    }
    
    private void HandlePlacement()
    {
        RaycastHit hit;
        bool hasHit = GetScreenCenterRaycast(out hit);
        
        if (firstClickPoint == null)
        {
            // First click - set start point
            if (hasHit || createAnchorsIfNoHit)
            {
                SetFirstPoint(hasHit ? hit.point : GetFallbackPosition(), hasHit ? hit.transform : null);
            }
        }
        else
        {
            // Second click - create rope
            if (isPlacementValid)
            {
                Vector3 endPoint = hasHit ? hit.point : GetFallbackPosition();
                Transform endAttachment = hasHit ? hit.transform : null;
                CreateRope(firstClickPoint.Value, endPoint, firstAttachment, endAttachment);
                ResetPlacement();
            }
        }
    }
    
    private bool GetScreenCenterRaycast(out RaycastHit hit)
    {
        if (playerCamera == null)
        {
            Debug.LogError("No camera found!");
            hit = default(RaycastHit);
            return false;
        }
        
        Vector3 screenCenter = new Vector3(Screen.width / 2f, Screen.height / 2f, 0f);
        Ray ray = playerCamera.ScreenPointToRay(screenCenter);
        
        return Physics.Raycast(ray, out hit, maxPlacementDistance, clickableLayers);
    }
    
    private Vector3 GetFallbackPosition()
    {
        // Place point in front of camera if no surface hit
        return playerCamera.transform.position + playerCamera.transform.forward * (maxPlacementDistance * 0.5f);
    }
    
    private void SetFirstPoint(Vector3 position, Transform attachment)
    {
        firstClickPoint = position;
        firstAttachment = attachment;
        
        // Create visual indicator if prefab is set
        if (anchorPointPrefab != null)
        {
            firstAnchorVisual = Instantiate(anchorPointPrefab, position, Quaternion.identity);
        }
        
        Debug.Log($"First rope point set at {position}");
    }
    
    private void CreateRope(Vector3 startPos, Vector3 endPos, Transform startAttachment, Transform endAttachment)
    {
        // Create rope GameObject
        GameObject ropeObject = new GameObject($"DynamicRope_{createdRopes.Count}");
        createdRopes.Add(ropeObject);
        
        // Add and configure CableComponent
        CableComponent cable = ropeObject.AddComponent<CableComponent>();

        // Add RopeStaticMeshCapture component for settling detection and capture
        RopeStaticMeshCapture captureComponent = ropeObject.AddComponent<RopeStaticMeshCapture>();
        
        // Create or use anchor points
        GameObject startAnchor = CreateAnchor("RopeStart", startPos, startAttachment);
        GameObject endAnchor = CreateAnchor("RopeEnd", endPos, endAttachment);
        
        // Configure cable properties
        cable.StartPoint = startAnchor.transform;
        cable.EndPoint = endAnchor.transform;
        
        // Calculate cable length
        float distance = Vector3.Distance(startPos, endPos);
        if (autoCalculateLength)
        {
            cable.CableLength = distance * lengthMultiplier; // Add slack
        }
        else
        {
            cable.CableLength = defaultRopeLength;
        }
        
        // Apply physics settings
        ConfigureCablePhysics(cable);
        
        // Set material
        if (ropeMaterial != null)
        {
            MeshRenderer meshRenderer = ropeObject.GetComponent<MeshRenderer>();
            if (meshRenderer != null)
                meshRenderer.sharedMaterial = ropeMaterial;
        }
        
        // Initialize the cable
        cable.ReinitializeCable();
        
        Debug.Log($"Cable created: {startPos} to {endPos}, Length: {cable.CableLength:F2}m");
    }
    
    private GameObject CreateAnchor(string name, Vector3 position, Transform attachment)
    {
        GameObject anchor = new GameObject(name);
        anchor.transform.position = position;
        
        if (attachToRigidbodies && attachment != null)
        {
            // Check if attachment has a rigidbody
            Rigidbody rb = attachment.GetComponent<Rigidbody>();
            if (rb != null)
            {
                // Make anchor a child of the rigidbody
                anchor.transform.SetParent(attachment);
            }
            else
            {
                // Just position it at the hit point
                anchor.transform.position = position;
                
                // Optionally parent to static geometry
                if (!attachment.CompareTag("Player"))
                {
                    anchor.transform.SetParent(attachment);
                }
            }
        }
        
        return anchor;
    }
    
    private void ConfigureCablePhysics(CableComponent cable)
    {
        // Apply saved physics settings
        cable.GetType().GetField("_TotalSegments", 
            System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance)
            ?.SetValue(cable, ropeSegments);
            
        cable.GetType().GetField("_Radius", 
            System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance)
            ?.SetValue(cable, ropeRadius);
            
        cable.GetType().GetField("_CableMass", 
            System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance)
            ?.SetValue(cable, ropeMass);
            
        cable.GetType().GetField("_Stiffness", 
            System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance)
            ?.SetValue(cable, ropeStiffness);
            
        cable.EnableCollisions = enableCollisions;
        cable.CollisionLayers = collisionLayers;
    }
    
    private void CancelPlacement()
    {
        ResetPlacement();
        Debug.Log("Rope placement cancelled");
    }
    
    private void ResetPlacement()
    {
        firstClickPoint = null;
        firstAttachment = null;
        
        // Clean up visual indicators
        if (firstAnchorVisual != null)
        {
            Destroy(firstAnchorVisual);
            firstAnchorVisual = null;
        }
        
        // Hide preview line
        if (previewLine != null)
            previewLine.enabled = false;
    }
    
    private void UndoLastRope()
    {
        if (createdRopes.Count > 0)
        {
            GameObject lastRope = createdRopes[createdRopes.Count - 1];
            createdRopes.RemoveAt(createdRopes.Count - 1);
            
            // Also destroy anchor points
            CableComponent cable = lastRope.GetComponent<CableComponent>();
            if (cable != null)
            {
                if (cable.StartPoint != null && cable.StartPoint.childCount == 0)
                    Destroy(cable.StartPoint.gameObject);
                if (cable.EndPoint != null && cable.EndPoint.childCount == 0)
                    Destroy(cable.EndPoint.gameObject);
            }
            
            Destroy(lastRope);
            Debug.Log("Removed last rope");
        }
    }
    
    private void ClearAllRopes()
    {
        foreach (GameObject rope in createdRopes)
        {
            if (rope != null)
            {
                // Clean up anchors
                CableComponent cable = rope.GetComponent<CableComponent>();
                if (cable != null)
                {
                    if (cable.StartPoint != null)
                        Destroy(cable.StartPoint.gameObject);
                    if (cable.EndPoint != null)
                        Destroy(cable.EndPoint.gameObject);
                }
                
                Destroy(rope);
            }
        }
        
        createdRopes.Clear();
        Debug.Log("Cleared all ropes");
    }
    
    private void CreatePreviewLine()
    {
        GameObject previewObj = new GameObject("RopePreview");
        previewObj.transform.SetParent(transform);
        
        previewLine = previewObj.AddComponent<LineRenderer>();
        
        if (previewLineMaterial == null)
        {
            // Create default material if none assigned
            previewLineMaterial = new Material(Shader.Find("Sprites/Default"));
        }
        
        previewLine.material = previewLineMaterial;
        previewLine.startWidth = previewLineWidth;
        previewLine.endWidth = previewLineWidth;
        previewLine.positionCount = 2;
        previewLine.enabled = false;
        previewLine.sortingOrder = 100;
    }
    
    private void UpdatePreviewLine()
    {
        if (firstClickPoint != null && previewLine != null)
        {
            RaycastHit hit;
            bool hasHit = GetScreenCenterRaycast(out hit);
            Vector3 currentPos = hasHit ? hit.point : GetFallbackPosition();
            
            // Check if placement would be valid
            float distance = Vector3.Distance(firstClickPoint.Value, currentPos);
            isPlacementValid = distance > 0.1f && distance < maxPlacementDistance;
            
            // Update preview line
            previewLine.enabled = true;
            previewLine.SetPosition(0, firstClickPoint.Value);
            previewLine.SetPosition(1, currentPos);
            
            // Update color based on validity
            Color previewColor = isPlacementValid ? validPlacementColor : invalidPlacementColor;
            previewLine.startColor = previewColor;
            previewLine.endColor = previewColor;
            
            // Draw catenary curve preview (optional, for realism)
            if (isPlacementValid && autoCalculateLength)
            {
                UpdatePreviewCurve(firstClickPoint.Value, currentPos, distance * lengthMultiplier);
            }
        }
        else
        {
            if (previewLine != null)
                previewLine.enabled = false;
        }
    }
    
    private void UpdatePreviewCurve(Vector3 start, Vector3 end, float length)
    {
        // Create a simple catenary preview with more points
        int curvePoints = 10;
        previewLine.positionCount = curvePoints;
        
        Vector3 delta = end - start;
        float sag = Mathf.Max(0, (length - delta.magnitude) * 0.5f);
        
        for (int i = 0; i < curvePoints; i++)
        {
            float t = (float)i / (curvePoints - 1);
            Vector3 point = Vector3.Lerp(start, end, t);
            
            // Add sag
            float sagCurve = 4f * t * (1f - t);
            point.y -= sagCurve * sag;
            
            previewLine.SetPosition(i, point);
        }
    }
    
    void OnGUI()
    {
        // UI instructions
        GUIStyle style = new GUIStyle(GUI.skin.label);
        style.fontSize = 14;
        style.normal.textColor = Color.white;
        
        GUILayout.BeginArea(new Rect(10, 10, 400, 150));
        
        GUILayout.Label("=== ROPE PLACEMENT TOOL ===", style);
        
        if (firstClickPoint == null)
        {
            GUILayout.Label("• Press [E] to set first rope point", style);
            GUILayout.Label("• Aim at surfaces to attach rope", style);
        }
        else
        {
            GUILayout.Label("• Press [E] to set second rope point", style);
            GUILayout.Label("• Press [ESC] or [Right Click] to cancel", style);
            
            if (!isPlacementValid)
            {
                style.normal.textColor = invalidPlacementColor;
                GUILayout.Label("! Position invalid - too close or too far", style);
            }
        }
        
        style.normal.textColor = Color.gray;
        GUILayout.Label($"• [Ctrl+Z] Undo last rope ({createdRopes.Count} active)", style);
        GUILayout.Label("• [Shift+C] Clear all ropes", style);
        
        GUILayout.EndArea();
    }
    
    void OnDrawGizmos()
    {
        // Draw placement range
        if (playerCamera != null && firstClickPoint != null)
        {
            Gizmos.color = new Color(0, 1, 0, 0.1f);
            Gizmos.DrawWireSphere(firstClickPoint.Value, maxPlacementDistance);
        }
    }
}