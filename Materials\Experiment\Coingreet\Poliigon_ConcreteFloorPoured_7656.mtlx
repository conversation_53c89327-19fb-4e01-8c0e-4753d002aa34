<?xml version="1.0"?>
<materialx version="1.38"><nodegraph name="Poliigon_ConcreteFloorPoured_7656_NG" xpos="-10" ypos="0"><constant name="Schema" type="string" xpos="0" ypos="0"><input name="value" type="string" value="9e709cb6-176d-4a9c-bbd4-7b828ab0de6c"/></constant><input name="UV_Tiling" type="vector2" value="1, 1"/><constant name="UV_Node" type="vector2" xpos="-20" ypos="0"><input name="value" type="vector2" value="1, 1" interfacename="UV_Tiling"/></constant><tiledimage name="BaseColor_Map" type="color3" xpos="-15" ypos="3"><input name="file" type="filename" value="Poliigon_ConcreteFloorPoured_7656_BaseColor.jpg" colorspace="srgb_texture"/><input name="default" type="color3" value="0, 0, 0"/><input name="uvtiling" type="vector2" value="2, 2" nodename="UV_Node"/></tiledimage><output name="BaseColor_Map_out" type="color3" nodename="BaseColor_Map"/><tiledimage name="Displacement_Map" type="float" xpos="-15" ypos="6"><input name="file" type="filename" value="Poliigon_ConcreteFloorPoured_7656_Displacement.tiff" colorspace="linear_texture"/><input name="default" type="float" value="0"/><input name="uvtiling" type="vector2" value="2, 2" nodename="UV_Node"/></tiledimage><displacement name="Displacement" type="displacementshader" xpos="-10" ypos="6"><input name="displacement" type="float" value="0" nodename="Displacement_Map"/><input name="scale" type="float" value="0"/></displacement><output name="Displacement_out" type="displacementshader" nodename="Displacement"/><tiledimage name="Metallic_Map" type="float" xpos="-15" ypos="15"><input name="file" type="filename" value="Poliigon_ConcreteFloorPoured_7656_Metallic.jpg" colorspace="linear_texture"/><input name="default" type="float" value="0"/><input name="uvtiling" type="vector2" value="2, 2" nodename="UV_Node"/></tiledimage><output name="Metallic_Map_out" type="float" nodename="Metallic_Map"/><input name="Normal_Strength" type="float" value="1"/><tiledimage name="Normal_Map" type="vector3" xpos="-15" ypos="18"><input name="file" type="filename" value="Poliigon_ConcreteFloorPoured_7656_Normal.png" colorspace="srgb_texture"/><input name="default" type="vector3" value="0, 0, 0"/><input name="uvtiling" type="vector2" value="2, 2" nodename="UV_Node"/></tiledimage><normalmap name="Normalmap" type="vector3" xpos="-10" ypos="18"><input name="in" type="vector3" value="0.5, 0.5, 1" nodename="Normal_Map"/><input name="space" type="string" value="tangent"/><input name="scale" type="float" value="1" interfacename="Normal_Strength"/><input name="normal" type="vector3" value="0, 0, 0"/><input name="tangent" type="vector3" value="0, 0, 0"/></normalmap><output name="Normalmap_out" type="vector3" nodename="Normalmap"/><tiledimage name="Roughness_Map" type="float" xpos="-15" ypos="24"><input name="file" type="filename" value="Poliigon_ConcreteFloorPoured_7656_Roughness.jpg" colorspace="linear_texture"/><input name="default" type="float" value="0"/><input name="uvtiling" type="vector2" value="2, 2" nodename="UV_Node"/></tiledimage><output name="Roughness_Map_out" type="float" nodename="Roughness_Map"/></nodegraph><surfacematerial name="USD_Default" type="material" xpos="0" ypos="0"><input name="surfaceshader" type="surfaceshader" nodename="Poliigon_ConcreteFloorPoured_7656"/><input name="displacementshader" type="displacementshader" nodegraph="Poliigon_ConcreteFloorPoured_7656_NG" output="Displacement_out"/></surfacematerial><standard_surface name="Poliigon_ConcreteFloorPoured_7656" type="surfaceshader" xpos="-5" ypos="0"><input name="base" type="float" value="1"/><input name="base_color" type="color3" value="1, 0, 0" nodegraph="Poliigon_ConcreteFloorPoured_7656_NG" output="BaseColor_Map_out"/><input name="base_color" type="color3" value="1, 0, 0"/><input name="diffuse_roughness" type="float" value="0"/><input name="metalness" type="float" value="0" nodegraph="Poliigon_ConcreteFloorPoured_7656_NG" output="Metallic_Map_out"/><input name="metalness" type="float" value="0"/><input name="specular" type="float" value="1"/><input name="specular_color" type="color3" value="1, 1, 1"/><input name="specular_roughness" type="float" value="0.35" nodegraph="Poliigon_ConcreteFloorPoured_7656_NG" output="Roughness_Map_out"/><input name="specular_roughness" type="float" value="0.35"/><input name="specular_IOR" type="float" value="1.5"/><input name="specular_anisotropy" type="float" value="0"/><input name="specular_rotation" type="float" value="0"/><input name="transmission" type="float" value="0"/><input name="transmission_color" type="color3" value="1, 1, 1"/><input name="transmission_depth" type="float" value="0"/><input name="transmission_scatter" type="color3" value="0, 0, 0"/><input name="transmission_scatter_anisotropy" type="float" value="0"/><input name="transmission_dispersion" type="float" value="0"/><input name="transmission_extra_roughness" type="float" value="0"/><input name="subsurface" type="float" value="0"/><input name="subsurface_color" type="color3" value="1, 1, 1" nodegraph="Poliigon_ConcreteFloorPoured_7656_NG" output="BaseColor_Map_out"/><input name="subsurface_color" type="color3" value="1, 1, 1"/><input name="subsurface_radius" type="color3" value="1, 1, 1"/><input name="subsurface_scale" type="float" value="1"/><input name="subsurface_anisotropy" type="float" value="0"/><input name="sheen" type="float" value="0"/><input name="sheen_color" type="color3" value="1, 1, 1"/><input name="sheen_roughness" type="float" value="0.3"/><input name="coat" type="float" value="0"/><input name="coat_color" type="color3" value="0.6, 0.45, 1"/><input name="coat_roughness" type="float" value="0.1"/><input name="coat_anisotropy" type="float" value="0"/><input name="coat_rotation" type="float" value="0"/><input name="coat_IOR" type="float" value="2.5"/><input name="coat_normal" type="vector3" value="0, 0, 0"/><input name="coat_affect_color" type="float" value="0"/><input name="coat_affect_roughness" type="float" value="0"/><input name="thin_film_thickness" type="float" value="0"/><input name="thin_film_IOR" type="float" value="1.5"/><input name="emission" type="float" value="0"/><input name="emission_color" type="color3" value="1, 1, 1"/><input name="opacity" type="color3" value="1, 1, 1"/><input name="thin_walled" type="boolean" value="false"/><input name="normal" type="vector3" value="0, 0, 0" nodegraph="Poliigon_ConcreteFloorPoured_7656_NG" output="Normalmap_out"/><input name="normal" type="vector3" value="0, 0, 0"/><input name="tangent" type="vector3" value="0, 0, 0"/></standard_surface></materialx>
