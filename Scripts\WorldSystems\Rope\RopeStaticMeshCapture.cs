using System;
using System.Collections.Generic;
using UnityEngine;
#if UNITY_EDITOR
using UnityEditor;
#endif

[System.Serializable]
public class RopeStateData
{
    public Vector3[] positions;
    public float radius;
    public int sides;
    public string captureTime;
    public string ropeName;
    
    public RopeStateData(Vector3[] pos, float rad, int s, string name)
    {
        positions = pos;
        radius = rad;
        sides = s;
        captureTime = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
        ropeName = name;
    }
}

[RequireComponent(typeof(CableComponent))]
public class RopeStaticMeshCapture : MonoBehaviour
{
    [Header("Capture Settings")]
    [SerializeField] private KeyCode captureKey = KeyCode.Return;
    [SerializeField] private bool captureOnKey = true;
    [SerializeField] private string meshNamePrefix = "CapturedRope";
    [SerializeField] private bool requireSettled = true;
    [SerializeField] private bool destroyDynamicAfterCapture = true;
    
    [Header("Settling Detection")]
    [SerializeField] private float settleVelocityThreshold = 0.01f;
    [SerializeField] private float settleTimeRequired = 2f;
    [SerializeField] private bool showSettleStatus = true;

    [Header("Mesh Settings")]
    [SerializeField] private int meshSides = 8;
    [SerializeField] private float meshRadius = 0.05f;
    [SerializeField] private bool addEndCaps = true;
    [SerializeField] private bool optimizeMesh = true;
    
    [Header("Saved State")]
    [SerializeField] private RopeStateData savedState;
    [SerializeField] private Mesh lastCapturedMesh;
    
    [Header("Static Mesh Object")]
    [SerializeField] private GameObject staticMeshPrefab;
    [SerializeField] private Material staticMeshMaterial;
    
    private CableComponent cableComponent;
    private List<GameObject> createdStaticMeshes = new List<GameObject>();
    
    // Settling detection state
    private float currentSettleTime = 0f;
    private bool isRopeSettled = false;
    private float lastAverageVelocity = 0f;
    
    void Start()
    {
        cableComponent = GetComponent<CableComponent>();
        
        if (cableComponent == null)
        {
            Debug.LogError("RopeStaticMeshCapture requires a CableComponent!");
            enabled = false;
            return;
        }
        
        // Load any saved rope state from PlayerPrefs
        LoadSavedState();
    }
    
    void Update()
    {
        // Update settling detection
        if (requireSettled)
        {
            CheckRopeSettled();
        }
        
        if (captureOnKey && Input.GetKeyDown(captureKey))
        {
            CaptureCurrentState();
        }
        
        // Debug keys
        if (Input.GetKeyDown(KeyCode.G) && Input.GetKey(KeyCode.LeftControl))
        {
            if (savedState != null)
            {
                CreateStaticMeshFromSavedState();
            }
        }
        
        if (Input.GetKeyDown(KeyCode.H) && Input.GetKey(KeyCode.LeftControl))
        {
            ClearAllStaticMeshes();
        }
    }
    
    private void CheckRopeSettled()
    {
        if (cableComponent == null) return;
        
        // Get cable particles using helper method
        CableParticle[] particles = GetCableParticles();
        if (particles == null) return;
        
        // Calculate average velocity of all free rope particles
        float totalVelocity = 0f;
        int freeParticleCount = 0;
        
        foreach (var particle in particles)
        {
            if (particle.IsFree()) // Only check free particles (not bound to endpoints)
            {
                totalVelocity += particle.Velocity.magnitude;
                freeParticleCount++;
            }
        }
        
        if (freeParticleCount > 0)
        {
            lastAverageVelocity = totalVelocity / freeParticleCount;
            
            // Check if velocity is below threshold
            if (lastAverageVelocity < settleVelocityThreshold)
            {
                currentSettleTime += Time.deltaTime;
                isRopeSettled = currentSettleTime >= settleTimeRequired;
            }
            else
            {
                currentSettleTime = 0f;
                isRopeSettled = false;
            }
        }
        else
        {
            // If no free particles, consider it settled (shouldn't happen normally)
            isRopeSettled = true;
        }
    }
    
    [ContextMenu("Capture Current Rope State")]
    public void CaptureCurrentState()
    {
        if (cableComponent == null) return;
        
        // Check if rope must be settled before capture
        if (requireSettled && !isRopeSettled)
        {
            Debug.LogWarning("Rope must be settled before capture! Wait for it to stop moving.");
            return;
        }
        
        // Get positions from cable component
        int segmentCount = GetSegmentCount();
        Vector3[] positions = new Vector3[segmentCount + 1];
        
        for (int i = 0; i <= segmentCount; i++)
        {
            positions[i] = cableComponent.GetPointPosition(i);
        }
        
        // Store the state
        savedState = new RopeStateData(
            positions,
            meshRadius,
            meshSides,
            gameObject.name
        );
        
        // Generate mesh
        lastCapturedMesh = GenerateMeshFromPositions(positions, meshRadius, meshSides);
        
        // Save to persistent storage
        SaveStateToPrefs();
        
        Debug.Log($"Captured rope state with {positions.Length} points at {savedState.captureTime}");
        
        // Optionally create static mesh immediately
        CreateStaticMeshObject(lastCapturedMesh, positions);
        
        // Optionally destroy the dynamic rope after capture
        if (destroyDynamicAfterCapture)
        {
            Debug.Log("Destroying dynamic rope after capture...");
            Destroy(gameObject, 0.1f); // Small delay to ensure mesh is created
        }
    }
    
    private int GetSegmentCount()
    {
        // Use reflection to get the segment count from CableComponent
        var segmentsField = cableComponent.GetType().GetField("_Segments", 
            System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
        
        if (segmentsField != null)
        {
            return (int)segmentsField.GetValue(cableComponent);
        }
        
        // Fallback - try to detect from particle count
        for (int i = 0; i < 100; i++)
        {
            if (cableComponent.GetPointPosition(i) == Vector3.zero && i > 0)
            {
                return i - 1;
            }
        }
        
        return 24; // Default
    }
    
    private CableParticle[] GetCableParticles()
    {
        // Helper method to get the private _Points array from CableComponent
        var pointsField = cableComponent.GetType().GetField("_Points", 
            System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
        
        if (pointsField != null)
        {
            return pointsField.GetValue(cableComponent) as CableParticle[];
        }
        
        return null;
    }

    private Mesh GenerateMeshFromPositions(Vector3[] positions, float radius, int sides)
    {
        if (positions == null || positions.Length < 2)
        {
            Debug.LogError("Need at least 2 positions to generate mesh!");
            return null;
        }

        Mesh mesh = new Mesh();
        mesh.name = $"{meshNamePrefix}_{DateTime.Now:yyyyMMdd_HHmmss}";

        List<Vector3> vertices = new List<Vector3>();
        List<int> triangles = new List<int>();
        List<Vector2> uvs = new List<Vector2>();

        // Generate tube mesh
        for (int i = 0; i < positions.Length; i++)
        {
            Vector3[] circle = CalculateCircleVertices(positions, i, radius, sides);

            // Add vertices for this ring
            for (int j = 0; j < sides; j++)
            {
                vertices.Add(circle[j]);

                float u = (float)j / (sides - 1);
                float v = (float)i / (positions.Length - 1);
                uvs.Add(new Vector2(u, v));
            }
        }

        // Generate triangles for tube (fixed winding order)
        for (int segment = 0; segment < positions.Length - 1; segment++)
        {
            for (int side = 0; side < sides; side++)
            {
                int current = segment * sides + side;
                int next = current + sides;
                int currentNextSide = (side == sides - 1) ? (segment * sides) : (current + 1);
                int nextNextSide = (side == sides - 1) ? ((segment + 1) * sides) : (next + 1);

                // First triangle (counter-clockwise winding)
                triangles.Add(current);
                triangles.Add(currentNextSide);
                triangles.Add(next);

                // Second triangle (counter-clockwise winding)
                triangles.Add(currentNextSide);
                triangles.Add(nextNextSide);
                triangles.Add(next);
            }
        }

        // Add end caps if requested
        if (addEndCaps)
        {
            AddEndCap(vertices, triangles, uvs, positions[0], positions, 0, radius, sides, true);
            AddEndCap(vertices, triangles, uvs, positions[positions.Length - 1], positions,
                positions.Length - 1, radius, sides, false);
        }

        // Apply to mesh
        mesh.vertices = vertices.ToArray();
        mesh.triangles = triangles.ToArray();
        mesh.uv = uvs.ToArray();

        // Recalculate normals and bounds
        mesh.RecalculateNormals();
        mesh.RecalculateBounds();
        mesh.RecalculateTangents();

        if (optimizeMesh)
        {
            mesh.Optimize();
        }

        return mesh;
    }

    private Vector3[] CalculateCircleVertices(Vector3[] positions, int index, float radius, int sides)
    {
        Vector3 position = positions[index];

        // Calculate forward direction
        Vector3 forward = Vector3.zero;
        if (index > 0)
            forward += (positions[index] - positions[index - 1]).normalized;
        if (index < positions.Length - 1)
            forward += (positions[index + 1] - positions[index]).normalized;

        if (forward.magnitude < 0.001f)
            forward = Vector3.forward;
        else
            forward = forward.normalized;

        // Calculate perpendicular vectors
        Vector3 up = Vector3.up;
        if (Mathf.Abs(Vector3.Dot(forward, up)) > 0.99f)
            up = Vector3.right;

        Vector3 right = Vector3.Cross(forward, up).normalized;
        up = Vector3.Cross(forward, right).normalized;

        // Generate circle vertices
        Vector3[] circle = new Vector3[sides];
        float angleStep = (2f * Mathf.PI) / sides;

        for (int i = 0; i < sides; i++)
        {
            float angle = i * angleStep;
            float x = Mathf.Cos(angle) * radius;
            float y = Mathf.Sin(angle) * radius;
            circle[i] = position + right * x + up * y;
        }

        return circle;
    }

    private void AddEndCap(List<Vector3> vertices, List<int> triangles, List<Vector2> uvs,
        Vector3 center, Vector3[] positions, int posIndex, float radius, int sides, bool isStart)
    {
        // Calculate cap direction
        Vector3 capNormal;
        if (isStart)
        {
            capNormal = (positions[0] - positions[1]).normalized;
        }
        else
        {
            capNormal = (positions[positions.Length - 1] - positions[positions.Length - 2]).normalized;
        }

        // Add center vertex
        int centerIndex = vertices.Count;
        vertices.Add(center);
        uvs.Add(new Vector2(0.5f, 0.5f));

        // Get ring vertices indices
        int ringStartIndex = posIndex * sides;

        // Create triangles for cap (fixed winding order)
        for (int i = 0; i < sides; i++)
        {
            int current = ringStartIndex + i;
            int next = ringStartIndex + ((i + 1) % sides);

            if (isStart)
            {
                // Start cap faces inward (reverse winding)
                triangles.Add(centerIndex);
                triangles.Add(current);
                triangles.Add(next);
            }
            else
            {
                // End cap faces outward (normal winding)
                triangles.Add(centerIndex);
                triangles.Add(next);
                triangles.Add(current);
            }
        }
    }

    private void CreateStaticMeshObject(Mesh mesh, Vector3[] positions)
    {
        if (mesh == null) return;

        GameObject staticRope = new GameObject($"StaticRope_{createdStaticMeshes.Count}");
        createdStaticMeshes.Add(staticRope);

        MeshFilter meshFilter = staticRope.AddComponent<MeshFilter>();
        MeshRenderer meshRenderer = staticRope.AddComponent<MeshRenderer>();

        meshFilter.mesh = mesh;

        // Apply material
        if (staticMeshMaterial != null)
        {
            meshRenderer.material = staticMeshMaterial;
        }
        else
        {
            // Try to get material from cable component
            MeshRenderer cableRenderer = GetComponent<MeshRenderer>();
            if (cableRenderer != null && cableRenderer.sharedMaterial != null)
            {
                meshRenderer.material = cableRenderer.sharedMaterial;
            }
        }

        // Optionally add mesh collider for physics interaction
        if (Input.GetKey(KeyCode.LeftShift))
        {
            MeshCollider meshCollider = staticRope.AddComponent<MeshCollider>();
            meshCollider.sharedMesh = mesh;
            meshCollider.convex = false; // Keep as concave for accurate collision
        }

        Debug.Log($"Created static mesh object: {staticRope.name}");
    }

    [ContextMenu("Create Static Mesh From Saved State")]
    public void CreateStaticMeshFromSavedState()
    {
        if (savedState == null || savedState.positions == null)
        {
            Debug.LogWarning("No saved state to create mesh from!");
            return;
        }

        Mesh mesh = GenerateMeshFromPositions(
            savedState.positions,
            savedState.radius,
            savedState.sides
        );

        if (mesh != null)
        {
            CreateStaticMeshObject(mesh, savedState.positions);
        }
    }

    [ContextMenu("Clear All Static Meshes")]
    public void ClearAllStaticMeshes()
    {
        foreach (GameObject obj in createdStaticMeshes)
        {
            if (obj != null)
                DestroyImmediate(obj);
        }
        createdStaticMeshes.Clear();
        Debug.Log("Cleared all static mesh objects");
    }

    #region Persistence

    private void SaveStateToPrefs()
    {
        if (savedState == null) return;

        string key = $"RopeState_{gameObject.name}";
        string json = JsonUtility.ToJson(savedState);
        PlayerPrefs.SetString(key, json);
        PlayerPrefs.Save();

        Debug.Log($"Saved rope state to PlayerPrefs with key: {key}");
    }

    private void LoadSavedState()
    {
        string key = $"RopeState_{gameObject.name}";
        if (PlayerPrefs.HasKey(key))
        {
            string json = PlayerPrefs.GetString(key);
            savedState = JsonUtility.FromJson<RopeStateData>(json);
            Debug.Log($"Loaded saved rope state from {savedState.captureTime}");
        }
    }

    public void ClearSavedState()
    {
        string key = $"RopeState_{gameObject.name}";
        if (PlayerPrefs.HasKey(key))
        {
            PlayerPrefs.DeleteKey(key);
            PlayerPrefs.Save();
            savedState = null;
            Debug.Log("Cleared saved rope state");
        }
    }

    #endregion

    void OnGUI()
    {
        if (!enabled) return;

        GUIStyle style = new GUIStyle(GUI.skin.label);
        style.fontSize = 12;
        style.normal.textColor = Color.white;

        GUILayout.BeginArea(new Rect(10, 200, 400, 250));

        GUILayout.Label("=== ROPE CAPTURE TOOL ===", style);

        // Show settling status if required
        if (requireSettled && showSettleStatus)
        {
            if (isRopeSettled)
            {
                style.normal.textColor = Color.green;
                GUILayout.Label("✓ ROPE SETTLED - Ready to capture!", style);
                style.normal.textColor = Color.white;
                GUILayout.Label($"• Press [ENTER] to capture and make permanent", style);
            }
            else
            {
                style.normal.textColor = Color.yellow;
                float settleProgress = currentSettleTime / settleTimeRequired;
                GUILayout.Label($"⏳ Rope settling... {settleProgress:P0}", style);
                GUILayout.Label($"   Velocity: {lastAverageVelocity:F4} (need < {settleVelocityThreshold:F3})", style);
                style.normal.textColor = Color.gray;
                GUILayout.Label("   Wait for rope to stop moving before capture", style);
            }
        }
        else
        {
            style.normal.textColor = Color.white;
            GUILayout.Label($"• Press [ENTER] to capture current rope state", style);
        }

        style.normal.textColor = Color.white;
        GUILayout.Label("• [Ctrl+G] Create static mesh from saved state", style);
        GUILayout.Label("• [Ctrl+H] Clear all static meshes", style);
        GUILayout.Label("• [Shift+ENTER] Capture with collision mesh", style);

        if (savedState != null)
        {
            style.normal.textColor = Color.green;
            GUILayout.Label($"✓ State saved: {savedState.positions.Length} points", style);
            GUILayout.Label($"  Captured: {savedState.captureTime}", style);
        }

        style.normal.textColor = Color.gray;
        GUILayout.Label($"Static meshes created: {createdStaticMeshes.Count}", style);

        if (destroyDynamicAfterCapture)
        {
            style.normal.textColor = Color.cyan;
            GUILayout.Label("⚠ Dynamic rope will be destroyed after capture", style);
        }

        GUILayout.EndArea();
    }

    #region Editor Tools

#if UNITY_EDITOR
    [ContextMenu("Save Mesh As Asset")]
    public void SaveMeshAsAsset()
    {
        if (lastCapturedMesh == null)
        {
            Debug.LogError("No captured mesh to save! Capture the rope state first.");
            return;
        }

        string path = EditorUtility.SaveFilePanelInProject(
            "Save Rope Mesh",
            lastCapturedMesh.name,
            "asset",
            "Save the captured rope mesh as an asset"
        );

        if (!string.IsNullOrEmpty(path))
        {
            AssetDatabase.CreateAsset(lastCapturedMesh, path);
            AssetDatabase.SaveAssets();
            Debug.Log($"Saved mesh asset to: {path}");
        }
    }

    [ContextMenu("Export State As ScriptableObject")]
    public void ExportStateAsScriptableObject()
    {
        if (savedState == null)
        {
            Debug.LogError("No saved state to export!");
            return;
        }

        RopeStateAsset asset = ScriptableObject.CreateInstance<RopeStateAsset>();
        asset.stateData = savedState;

        string path = EditorUtility.SaveFilePanelInProject(
            "Save Rope State",
            $"RopeState_{savedState.ropeName}",
            "asset",
            "Save the rope state as a ScriptableObject"
        );

        if (!string.IsNullOrEmpty(path))
        {
            AssetDatabase.CreateAsset(asset, path);
            AssetDatabase.SaveAssets();
            Debug.Log($"Saved rope state asset to: {path}");
        }
    }
#endif

    #endregion
}

// ScriptableObject for persistent storage
[CreateAssetMenu(fileName = "RopeStateAsset", menuName = "Rope/Rope State Asset")]
public class RopeStateAsset : ScriptableObject
{
    public RopeStateData stateData;

    public Mesh GenerateMesh(float radius, int sides)
    {
        if (stateData == null || stateData.positions == null)
        {
            Debug.LogError("No state data in asset!");
            return null;
        }

        // This would need the mesh generation code from above
        // For brevity, returning null here
        return null;
    }
}
